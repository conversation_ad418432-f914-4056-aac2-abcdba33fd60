# AI对话日志系统设计文档

## 概述

为 `chat_with_ai` 业务功能设计的数据库记录表，用于追踪和记录AI对话的完整生命周期数据，支持业务查询、统计分析和数据追溯需求。

## 数据库表结构

### 表名：`ai_chat_logs`

| 字段名 | 数据类型 | 约束 | 描述 |
|--------|----------|------|------|
| id | UUID | PRIMARY KEY | 主键 |
| created_at | TIMESTAMPTZ | NOT NULL | 创建时间 |
| updated_at | TIMESTAMPTZ | NOT NULL | 更新时间 |
| user_id | UUID | NOT NULL, FK | 用户ID（外键关联users表） |
| user_identifier | VARCHAR(100) | NOT NULL | Dify用户标识 |
| query | TEXT | NOT NULL | 用户输入的问题 |
| inputs | JSONB | NULL | 应用变量值 |
| response_mode | VARCHAR(20) | NOT NULL | 响应模式(streaming/blocking) |
| auto_generate_name | BOOLEAN | NOT NULL | 是否自动生成会话标题 |
| files_info | JSONB | NULL | 上传文件信息 |
| conversation_id | VARCHAR(100) | NULL | Dify会话ID |
| message_id | VARCHAR(100) | NULL | Dify消息ID |
| parent_message_id | VARCHAR(100) | NULL | 父消息ID |
| answer | TEXT | NULL | AI回复内容 |
| status | VARCHAR(20) | NOT NULL | 处理状态 |
| error_message | TEXT | NULL | 错误信息 |
| response_time_ms | INTEGER | NULL | 响应时间(毫秒) |
| order_id | VARCHAR(100) | NULL | 预扣费订单ID |
| business_id | VARCHAR(100) | NULL | 业务ID |
| needs_new_charge | BOOLEAN | NOT NULL | 是否需要重新计费 |
| charge_status | VARCHAR(20) | NOT NULL | 计费状态 |
| moderation_passed | BOOLEAN | NOT NULL | 内容审核是否通过 |
| moderation_message | VARCHAR(500) | NULL | 审核失败原因 |
| request_ip | VARCHAR(45) | NULL | 请求IP地址 |
| user_agent | VARCHAR(500) | NULL | 用户代理 |
| session_info | JSONB | NULL | 会话相关信息 |
| extra_data | JSONB | NULL | 扩展数据 |

### 索引设计

```sql
-- 用户查询索引
CREATE INDEX idx_ai_chat_logs_user_created ON ai_chat_logs (user_id, created_at);

-- 会话查询索引
CREATE INDEX idx_ai_chat_logs_conversation_created ON ai_chat_logs (conversation_id, created_at);

-- 状态查询索引
CREATE INDEX idx_ai_chat_logs_status_created ON ai_chat_logs (status, created_at);

-- 计费查询索引
CREATE INDEX idx_ai_chat_logs_order_id ON ai_chat_logs (order_id);
CREATE INDEX idx_ai_chat_logs_message_id ON ai_chat_logs (message_id);
CREATE INDEX idx_ai_chat_logs_business_id ON ai_chat_logs (business_id);

-- 部分索引（仅对非空值建索引）
CREATE INDEX idx_ai_chat_logs_conversation_id_not_null ON ai_chat_logs (conversation_id) WHERE conversation_id IS NOT NULL;
CREATE INDEX idx_ai_chat_logs_message_id_not_null ON ai_chat_logs (message_id) WHERE message_id IS NOT NULL;
CREATE INDEX idx_ai_chat_logs_order_id_not_null ON ai_chat_logs (order_id) WHERE order_id IS NOT NULL;
```

## 状态枚举

### ChatStatus（对话状态）
- `pending`: 待处理
- `processing`: 处理中
- `success`: 成功
- `error`: 错误
- `timeout`: 超时
- `cancelled`: 已取消

### ChargeStatus（计费状态）
- `pending`: 待处理
- `confirmed`: 已确认
- `cancelled`: 已取消
- `failed`: 失败

## 使用方式

### 1. 数据库迁移

```bash
# 运行迁移创建表
aerich upgrade
```

### 2. 在代码中集成日志记录

#### 方式一：使用上下文管理器（推荐）

```python
from app.services.ai_chat_log_service import ChatLogContext

async def chat_with_ai(request: Request, chat_request: ChatMessageRequest):
    # 使用日志上下文管理器
    async with ChatLogContext(
        user=current_user,
        chat_request=chat_request,
        user_identifier=user_identifier,
        request=request,
        order_id=order_id,
        needs_new_charge=needs_new_charge,
        moderation_passed=moderation_passed
    ) as chat_log:
        # 业务逻辑处理
        # 异常会被自动记录到日志中
        pass
```

#### 方式二：手动调用服务方法

```python
from app.services.ai_chat_log_service import ai_chat_log_service

# 创建日志记录
chat_log = await ai_chat_log_service.create_chat_log(
    user=current_user,
    chat_request=chat_request,
    user_identifier=user_identifier,
    request=request
)

# 更新状态为处理中
await ai_chat_log_service.update_chat_log_processing(chat_log)

# 更新为成功状态
await ai_chat_log_service.update_chat_log_success(
    chat_log,
    answer=answer,
    message_id=message_id
)

# 更新为错误状态
await ai_chat_log_service.update_chat_log_error(
    chat_log,
    error_message="错误信息"
)
```

### 3. 查询和统计API

#### 获取对话日志列表
```
GET /api/ai-chat/logs?page=1&page_size=20&status=success
```

#### 获取对话统计信息
```
GET /api/ai-chat/statistics?days=7
```

#### 获取单个日志详情
```
GET /api/ai-chat/logs/{log_id}
```

#### 获取会话内的对话日志
```
GET /api/ai-chat/conversations/{conversation_id}/logs
```

## 业务价值

### 1. 数据追溯
- 完整记录每次AI对话的请求和响应
- 支持问题排查和用户反馈处理
- 提供审计日志功能

### 2. 统计分析
- 用户使用行为分析
- 系统性能监控
- 成功率和错误率统计
- 响应时间分析

### 3. 业务优化
- 识别高频问题和用户需求
- 优化AI模型和提示词
- 改进用户体验

### 4. 计费管理
- 关联计费订单和业务记录
- 支持计费审计和对账
- 异常计费处理

## 性能考虑

### 1. 数据量管理
- 建议定期归档历史数据（如6个月以上）
- 考虑分表策略（按时间或用户分表）
- 敏感数据定期清理

### 2. 查询优化
- 合理使用索引
- 避免全表扫描
- 分页查询限制

### 3. 写入优化
- 异步写入日志
- 批量写入优化
- 避免阻塞主业务流程

## 扩展性

### 1. 字段扩展
- `extra_data` JSON字段支持未来新增数据
- 状态枚举支持新增状态类型

### 2. 功能扩展
- 支持其他AI功能的日志记录
- 集成更多分析维度
- 支持实时监控和告警

## 安全考虑

### 1. 数据脱敏
- 敏感用户输入可考虑脱敏存储
- 定期清理个人隐私数据

### 2. 访问控制
- 用户只能查看自己的日志
- 管理员权限控制
- API访问频率限制

## 部署建议

1. **开发环境**：完整记录所有字段用于调试
2. **测试环境**：模拟生产环境的数据量进行性能测试
3. **生产环境**：
   - 配置数据保留策略
   - 监控表大小和查询性能
   - 设置告警阈值

## 维护建议

1. **定期监控**：
   - 表大小增长趋势
   - 查询性能指标
   - 错误率统计

2. **数据清理**：
   - 定期归档历史数据
   - 清理测试数据
   - 优化索引

3. **性能优化**：
   - 根据查询模式调整索引
   - 考虑读写分离
   - 缓存热点数据
