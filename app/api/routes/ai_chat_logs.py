"""
AI对话日志查询和统计API
提供对话历史查询、统计分析等功能
"""

from fastapi import APIRouter, Request, Query, HTTPException, status
from typing import Optional, List
from datetime import datetime, timedelta
from pydantic import BaseModel

from app.api.deps import get_current_user_from_state
from app.services.ai_chat_log_service import ai_chat_log_service
from app.models.ai_chat_log import ChatStatus, ChargeStatus
from app.utils.utils import ResponseModel, send_data
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


class ChatLogResponse(BaseModel):
    """对话日志响应模型"""
    id: str
    user_id: str
    user_identifier: str
    query: str
    answer: Optional[str] = None
    conversation_id: Optional[str] = None
    message_id: Optional[str] = None
    status: str
    response_mode: str
    response_time_ms: Optional[int] = None
    charge_status: str
    moderation_passed: bool
    created_at: str
    updated_at: str


class ChatLogListResponse(BaseModel):
    """对话日志列表响应模型"""
    logs: List[ChatLogResponse]
    total: int
    page: int
    page_size: int
    has_more: bool


class ChatStatisticsResponse(BaseModel):
    """对话统计响应模型"""
    total_count: int
    success_count: int
    error_count: int
    success_rate: float
    period: dict


@router.get("/logs", response_model=ResponseModel[ChatLogListResponse], summary="获取AI对话日志列表")
async def get_chat_logs(
    request: Request,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    conversation_id: Optional[str] = Query(None, description="会话ID过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间")
):
    """
    获取当前用户的AI对话日志列表
    
    Args:
        page: 页码
        page_size: 每页数量
        conversation_id: 会话ID过滤
        status: 状态过滤
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        对话日志列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取对话日志列表")
        
        # 验证状态参数
        chat_status = None
        if status:
            try:
                chat_status = ChatStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的状态值: {status}"
                )
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取日志列表
        logs = await ai_chat_log_service.get_user_chat_logs(
            user_id=str(current_user.id),
            limit=page_size + 1,  # 多获取一条用于判断是否有更多
            offset=offset,
            conversation_id=conversation_id,
            status=chat_status,
            start_time=start_time,
            end_time=end_time
        )
        
        # 判断是否有更多数据
        has_more = len(logs) > page_size
        if has_more:
            logs = logs[:-1]  # 移除多获取的那一条
        
        # 转换为响应格式
        log_responses = []
        for log in logs:
            log_responses.append(ChatLogResponse(
                id=str(log.id),
                user_id=str(log.user_id),
                user_identifier=log.user_identifier,
                query=log.query,
                answer=log.answer,
                conversation_id=log.conversation_id,
                message_id=log.message_id,
                status=log.status.value,
                response_mode=log.response_mode,
                response_time_ms=log.response_time_ms,
                charge_status=log.charge_status.value,
                moderation_passed=log.moderation_passed,
                created_at=log.created_at.isoformat(),
                updated_at=log.updated_at.isoformat()
            ))
        
        response_data = ChatLogListResponse(
            logs=log_responses,
            total=len(log_responses),
            page=page,
            page_size=page_size,
            has_more=has_more
        )
        
        logger.info(f"成功获取用户 {current_user.username} 的对话日志，数量: {len(log_responses)}")
        return send_data(True, response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取对话日志失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取对话日志失败: {error_msg}")
        return send_data(False, None, error_msg)


@router.get("/statistics", response_model=ResponseModel[ChatStatisticsResponse], summary="获取AI对话统计信息")
async def get_chat_statistics(
    request: Request,
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    start_time: Optional[datetime] = Query(None, description="开始时间"),
    end_time: Optional[datetime] = Query(None, description="结束时间")
):
    """
    获取当前用户的AI对话统计信息
    
    Args:
        days: 统计天数（如果未指定start_time和end_time）
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        对话统计信息
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取对话统计信息")
        
        # 确定时间范围
        if not start_time and not end_time:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
        elif not end_time:
            end_time = datetime.now()
        elif not start_time:
            start_time = end_time - timedelta(days=days)
        
        # 获取统计信息
        statistics = await ai_chat_log_service.get_chat_statistics(
            user_id=str(current_user.id),
            start_time=start_time,
            end_time=end_time
        )
        
        response_data = ChatStatisticsResponse(**statistics)
        
        logger.info(f"成功获取用户 {current_user.username} 的对话统计信息")
        return send_data(True, response_data)
        
    except Exception as e:
        error_msg = f"获取对话统计信息失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取对话统计信息失败: {error_msg}")
        return send_data(False, None, error_msg)


@router.get("/logs/{log_id}", response_model=ResponseModel[ChatLogResponse], summary="获取单个对话日志详情")
async def get_chat_log_detail(
    request: Request,
    log_id: str
):
    """
    获取单个对话日志的详细信息
    
    Args:
        log_id: 日志ID
        
    Returns:
        对话日志详情
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取对话日志详情: {log_id}")
        
        # 查询日志记录
        from app.models.ai_chat_log import AiChatLog
        log = await AiChatLog.filter(id=log_id, user_id=current_user.id).first()
        
        if not log:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="对话日志不存在"
            )
        
        # 转换为响应格式
        response_data = ChatLogResponse(
            id=str(log.id),
            user_id=str(log.user_id),
            user_identifier=log.user_identifier,
            query=log.query,
            answer=log.answer,
            conversation_id=log.conversation_id,
            message_id=log.message_id,
            status=log.status.value,
            response_mode=log.response_mode,
            response_time_ms=log.response_time_ms,
            charge_status=log.charge_status.value,
            moderation_passed=log.moderation_passed,
            created_at=log.created_at.isoformat(),
            updated_at=log.updated_at.isoformat()
        )
        
        logger.info(f"成功获取用户 {current_user.username} 的对话日志详情")
        return send_data(True, response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取对话日志详情失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取对话日志详情失败: {error_msg}")
        return send_data(False, None, error_msg)


@router.get("/conversations/{conversation_id}/logs", response_model=ResponseModel[ChatLogListResponse], summary="获取会话内的对话日志")
async def get_conversation_logs(
    request: Request,
    conversation_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    获取指定会话内的所有对话日志
    
    Args:
        conversation_id: 会话ID
        page: 页码
        page_size: 每页数量
        
    Returns:
        会话内的对话日志列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取会话对话日志: {conversation_id}")
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取会话内的日志
        logs = await ai_chat_log_service.get_user_chat_logs(
            user_id=str(current_user.id),
            limit=page_size + 1,
            offset=offset,
            conversation_id=conversation_id
        )
        
        # 判断是否有更多数据
        has_more = len(logs) > page_size
        if has_more:
            logs = logs[:-1]
        
        # 转换为响应格式
        log_responses = []
        for log in logs:
            log_responses.append(ChatLogResponse(
                id=str(log.id),
                user_id=str(log.user_id),
                user_identifier=log.user_identifier,
                query=log.query,
                answer=log.answer,
                conversation_id=log.conversation_id,
                message_id=log.message_id,
                status=log.status.value,
                response_mode=log.response_mode,
                response_time_ms=log.response_time_ms,
                charge_status=log.charge_status.value,
                moderation_passed=log.moderation_passed,
                created_at=log.created_at.isoformat(),
                updated_at=log.updated_at.isoformat()
            ))
        
        response_data = ChatLogListResponse(
            logs=log_responses,
            total=len(log_responses),
            page=page,
            page_size=page_size,
            has_more=has_more
        )
        
        logger.info(f"成功获取用户 {current_user.username} 的会话对话日志，数量: {len(log_responses)}")
        return send_data(True, response_data)
        
    except Exception as e:
        error_msg = f"获取会话对话日志失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取会话对话日志失败: {error_msg}")
        return send_data(False, None, error_msg)
