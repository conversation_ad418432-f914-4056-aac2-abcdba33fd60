import uuid
from tortoise import fields
from tortoise.models import Model
from enum import Enum


class ChatStatus(str, Enum):
    """AI对话状态枚举"""
    PENDING = "pending"           # 待处理
    PROCESSING = "processing"     # 处理中
    SUCCESS = "success"           # 成功
    ERROR = "error"               # 错误
    TIMEOUT = "timeout"           # 超时
    CANCELLED = "cancelled"       # 已取消


class ChargeStatus(str, Enum):
    """计费状态枚举"""
    PENDING = "pending"           # 待处理
    CONFIRMED = "confirmed"       # 已确认
    CANCELLED = "cancelled"       # 已取消
    FAILED = "failed"             # 失败


class AiChatLog(Model):
    """AI对话日志模型"""
    
    # 基础字段
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="主键")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    # 用户相关字段
    user_id = fields.UUIDField(description="用户ID")
    user = fields.ForeignKeyField(
        "models.User", 
        related_name="ai_chat_logs", 
        description="关联用户"
    )
    user_identifier = fields.CharField(max_length=100, description="Dify用户标识")
    
    # 请求信息字段
    query = fields.TextField(description="用户输入的问题")
    inputs = fields.JSONField(null=True, description="应用变量值")
    response_mode = fields.CharField(
        max_length=20, 
        default="streaming", 
        description="响应模式(streaming/blocking)"
    )
    auto_generate_name = fields.BooleanField(
        default=True, 
        description="是否自动生成会话标题"
    )
    files_info = fields.JSONField(null=True, description="上传文件信息")
    
    # 会话信息字段
    conversation_id = fields.CharField(
        max_length=100, 
        null=True, 
        description="Dify会话ID"
    )
    message_id = fields.CharField(
        max_length=100, 
        null=True, 
        description="Dify消息ID"
    )
    parent_message_id = fields.CharField(
        max_length=100, 
        null=True, 
        description="父消息ID"
    )
    
    # 响应信息字段
    answer = fields.TextField(null=True, description="AI回复内容")
    status = fields.CharEnumField(
        ChatStatus, 
        default=ChatStatus.PENDING, 
        description="处理状态"
    )
    error_message = fields.TextField(null=True, description="错误信息")
    response_time_ms = fields.IntField(null=True, description="响应时间(毫秒)")
    
    # 计费信息字段
    order_id = fields.CharField(max_length=100, null=True, description="预扣费订单ID")
    business_id = fields.CharField(max_length=100, null=True, description="业务ID")
    needs_new_charge = fields.BooleanField(
        default=True, 
        description="是否需要重新计费"
    )
    charge_status = fields.CharEnumField(
        ChargeStatus, 
        default=ChargeStatus.PENDING, 
        description="计费状态"
    )
    
    # 审核信息字段
    moderation_passed = fields.BooleanField(
        default=True, 
        description="内容审核是否通过"
    )
    moderation_message = fields.CharField(
        max_length=500, 
        null=True, 
        description="审核失败原因"
    )
    
    # 元数据字段
    request_ip = fields.CharField(max_length=45, null=True, description="请求IP地址")
    user_agent = fields.CharField(max_length=500, null=True, description="用户代理")
    session_info = fields.JSONField(null=True, description="会话相关信息")
    
    # 扩展字段
    extra_data = fields.JSONField(null=True, description="扩展数据")

    class Meta:
        table = "ai_chat_logs"
        table_description = "AI对话日志表"
        # 索引定义
        indexes = [
            # 用户查询索引
            ("user_id", "created_at"),
            # 会话查询索引  
            ("conversation_id", "created_at"),
            # 状态查询索引
            ("status", "created_at"),
            # 计费查询索引
            ("order_id",),
            # 消息ID索引
            ("message_id",),
        ]

    def __str__(self):
        return f"AiChatLog({self.user_identifier}, {self.conversation_id}, {self.status})"

    @property
    def is_successful(self) -> bool:
        """判断对话是否成功"""
        return self.status == ChatStatus.SUCCESS

    @property
    def has_error(self) -> bool:
        """判断是否有错误"""
        return self.status in [ChatStatus.ERROR, ChatStatus.TIMEOUT, ChatStatus.CANCELLED]

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "user_identifier": self.user_identifier,
            "query": self.query,
            "answer": self.answer,
            "conversation_id": self.conversation_id,
            "message_id": self.message_id,
            "status": self.status.value,
            "response_mode": self.response_mode,
            "response_time_ms": self.response_time_ms,
            "charge_status": self.charge_status.value,
            "moderation_passed": self.moderation_passed,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
