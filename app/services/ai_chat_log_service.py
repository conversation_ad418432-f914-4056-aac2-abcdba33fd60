"""
AI对话日志服务
负责记录和管理AI对话的完整生命周期数据
"""

import json
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from fastapi import Request

from app.models.ai_chat_log import AiChatLog, ChatStatus, ChargeStatus
from app.models.user import User
from app.core.logging import get_logger
from app.api.schemas.ai_chat import ChatMessageRequest

logger = get_logger(__name__)


class AiChatLogService:
    """AI对话日志服务"""

    @staticmethod
    async def create_chat_log(
        user: User,
        chat_request: ChatMessageRequest,
        user_identifier: str,
        request: Optional[Request] = None,
        order_id: Optional[str] = None,
        needs_new_charge: bool = True,
        moderation_passed: bool = True,
        moderation_message: Optional[str] = None
    ) -> AiChatLog:
        """
        创建AI对话日志记录
        
        Args:
            user: 用户对象
            chat_request: 对话请求
            user_identifier: Dify用户标识
            request: HTTP请求对象
            order_id: 预扣费订单ID
            needs_new_charge: 是否需要重新计费
            moderation_passed: 内容审核是否通过
            moderation_message: 审核失败原因
            
        Returns:
            创建的日志记录
        """
        try:
            # 提取请求元数据
            request_ip = None
            user_agent = None
            if request:
                request_ip = request.client.host if request.client else None
                user_agent = request.headers.get("user-agent")

            # 处理文件信息
            files_info = None
            if chat_request.files:
                files_info = [
                    {
                        "id": file.id,
                        "name": file.name,
                        "type": file.type,
                        "transfer_method": file.transfer_method,
                        "url": getattr(file, 'url', None)
                    }
                    for file in chat_request.files
                ]

            # 创建日志记录
            chat_log = await AiChatLog.create(
                user_id=user.id,
                user_identifier=user_identifier,
                query=chat_request.query,
                inputs=chat_request.inputs,
                response_mode=chat_request.response_mode,
                auto_generate_name=chat_request.auto_generate_name,
                files_info=files_info,
                conversation_id=chat_request.conversation_id,
                status=ChatStatus.PENDING,
                order_id=order_id,
                needs_new_charge=needs_new_charge,
                charge_status=ChargeStatus.PENDING if order_id else ChargeStatus.CANCELLED,
                moderation_passed=moderation_passed,
                moderation_message=moderation_message,
                request_ip=request_ip,
                user_agent=user_agent
            )

            logger.info(f"创建AI对话日志记录: {chat_log.id}, 用户: {user.username}")
            return chat_log

        except Exception as e:
            logger.error(f"创建AI对话日志失败: {str(e)}")
            raise

    @staticmethod
    async def update_chat_log_processing(
        chat_log: AiChatLog,
        message_id: Optional[str] = None,
        conversation_id: Optional[str] = None
    ) -> None:
        """
        更新日志状态为处理中
        
        Args:
            chat_log: 日志记录
            message_id: Dify消息ID
            conversation_id: Dify会话ID
        """
        try:
            update_data = {"status": ChatStatus.PROCESSING}
            
            if message_id:
                update_data["message_id"] = message_id
            if conversation_id:
                update_data["conversation_id"] = conversation_id

            await chat_log.update_from_dict(update_data)
            logger.debug(f"更新日志状态为处理中: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志处理状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_success(
        chat_log: AiChatLog,
        answer: str,
        message_id: Optional[str] = None,
        conversation_id: Optional[str] = None,
        business_id: Optional[str] = None,
        response_time_ms: Optional[int] = None
    ) -> None:
        """
        更新日志状态为成功
        
        Args:
            chat_log: 日志记录
            answer: AI回复内容
            message_id: Dify消息ID
            conversation_id: Dify会话ID
            business_id: 业务ID
            response_time_ms: 响应时间
        """
        try:
            update_data = {
                "status": ChatStatus.SUCCESS,
                "answer": answer
            }
            
            if message_id:
                update_data["message_id"] = message_id
            if conversation_id:
                update_data["conversation_id"] = conversation_id
            if business_id:
                update_data["business_id"] = business_id
            if response_time_ms:
                update_data["response_time_ms"] = response_time_ms

            await chat_log.update_from_dict(update_data)
            logger.info(f"更新日志状态为成功: {chat_log.id}")

        except Exception as e:
            logger.error(f"更新日志成功状态失败: {str(e)}")

    @staticmethod
    async def update_chat_log_error(
        chat_log: AiChatLog,
        error_message: str,
        status: ChatStatus = ChatStatus.ERROR,
        response_time_ms: Optional[int] = None
    ) -> None:
        """
        更新日志状态为错误
        
        Args:
            chat_log: 日志记录
            error_message: 错误信息
            status: 错误状态类型
            response_time_ms: 响应时间
        """
        try:
            update_data = {
                "status": status,
                "error_message": error_message
            }
            
            if response_time_ms:
                update_data["response_time_ms"] = response_time_ms

            await chat_log.update_from_dict(update_data)
            logger.warning(f"更新日志状态为错误: {chat_log.id}, 错误: {error_message}")

        except Exception as e:
            logger.error(f"更新日志错误状态失败: {str(e)}")

    @staticmethod
    async def update_charge_status(
        chat_log: AiChatLog,
        charge_status: ChargeStatus,
        business_id: Optional[str] = None
    ) -> None:
        """
        更新计费状态
        
        Args:
            chat_log: 日志记录
            charge_status: 计费状态
            business_id: 业务ID
        """
        try:
            update_data = {"charge_status": charge_status}
            
            if business_id:
                update_data["business_id"] = business_id

            await chat_log.update_from_dict(update_data)
            logger.debug(f"更新计费状态: {chat_log.id}, 状态: {charge_status}")

        except Exception as e:
            logger.error(f"更新计费状态失败: {str(e)}")

    @staticmethod
    async def get_user_chat_logs(
        user_id: str,
        limit: int = 20,
        offset: int = 0,
        conversation_id: Optional[str] = None,
        status: Optional[ChatStatus] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[AiChatLog]:
        """
        获取用户的对话日志
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            offset: 偏移量
            conversation_id: 会话ID过滤
            status: 状态过滤
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            日志记录列表
        """
        try:
            query = AiChatLog.filter(user_id=user_id)
            
            if conversation_id:
                query = query.filter(conversation_id=conversation_id)
            if status:
                query = query.filter(status=status)
            if start_time:
                query = query.filter(created_at__gte=start_time)
            if end_time:
                query = query.filter(created_at__lte=end_time)

            logs = await query.order_by("-created_at").offset(offset).limit(limit)
            return logs

        except Exception as e:
            logger.error(f"获取用户对话日志失败: {str(e)}")
            return []

    @staticmethod
    async def get_chat_statistics(
        user_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        获取对话统计信息
        
        Args:
            user_id: 用户ID（可选）
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            统计信息字典
        """
        try:
            query = AiChatLog.all()
            
            if user_id:
                query = query.filter(user_id=user_id)
            if start_time:
                query = query.filter(created_at__gte=start_time)
            if end_time:
                query = query.filter(created_at__lte=end_time)

            total_count = await query.count()
            success_count = await query.filter(status=ChatStatus.SUCCESS).count()
            error_count = await query.filter(status=ChatStatus.ERROR).count()
            
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            return {
                "total_count": total_count,
                "success_count": success_count,
                "error_count": error_count,
                "success_rate": round(success_rate, 2),
                "period": {
                    "start_time": start_time.isoformat() if start_time else None,
                    "end_time": end_time.isoformat() if end_time else None
                }
            }

        except Exception as e:
            logger.error(f"获取对话统计信息失败: {str(e)}")
            return {}


class ChatLogContext:
    """AI对话日志上下文管理器"""

    def __init__(
        self,
        user: User,
        chat_request: ChatMessageRequest,
        user_identifier: str,
        request: Optional[Request] = None,
        order_id: Optional[str] = None,
        needs_new_charge: bool = True,
        moderation_passed: bool = True,
        moderation_message: Optional[str] = None
    ):
        self.user = user
        self.chat_request = chat_request
        self.user_identifier = user_identifier
        self.request = request
        self.order_id = order_id
        self.needs_new_charge = needs_new_charge
        self.moderation_passed = moderation_passed
        self.moderation_message = moderation_message
        self.chat_log: Optional[AiChatLog] = None
        self.start_time = None

    async def __aenter__(self) -> AiChatLog:
        """进入上下文时创建日志记录"""
        self.start_time = time.time()
        self.chat_log = await AiChatLogService.create_chat_log(
            user=self.user,
            chat_request=self.chat_request,
            user_identifier=self.user_identifier,
            request=self.request,
            order_id=self.order_id,
            needs_new_charge=self.needs_new_charge,
            moderation_passed=self.moderation_passed,
            moderation_message=self.moderation_message
        )
        return self.chat_log

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出上下文时更新日志状态"""
        if self.chat_log and self.start_time:
            response_time_ms = int((time.time() - self.start_time) * 1000)

            if exc_type is not None:
                # 有异常发生，记录错误
                error_message = str(exc_val) if exc_val else "未知错误"
                await AiChatLogService.update_chat_log_error(
                    self.chat_log,
                    error_message=error_message,
                    response_time_ms=response_time_ms
                )
            elif self.chat_log.status == ChatStatus.PENDING:
                # 如果状态仍然是待处理，说明可能有问题
                await AiChatLogService.update_chat_log_error(
                    self.chat_log,
                    error_message="处理未完成",
                    response_time_ms=response_time_ms
                )


# 全局服务实例
ai_chat_log_service = AiChatLogService()
