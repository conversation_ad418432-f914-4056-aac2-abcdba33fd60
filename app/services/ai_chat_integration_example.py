"""
AI对话日志集成示例
展示如何在 chat_with_ai 函数中集成日志记录功能
"""

import asyncio
import json
import time
from fastapi import Request
from fastapi.responses import StreamingResponse

from app.models.user import User
from app.api.schemas.ai_chat import ChatMessageRequest
from app.services.ai_chat_log_service import ChatLogContext, ai_chat_log_service, ChatStatus, ChargeStatus
from app.services.dify_service import DifyService
from app.services.wallet.service import wallet_service
from app.core.logging import get_logger

logger = get_logger(__name__)


async def chat_with_ai_with_logging(
    request: Request,
    chat_request: ChatMessageRequest,
    current_user: User,
    dify_service: DifyService,
    order_id: str = None,
    needs_new_charge: bool = True,
    moderation_passed: bool = True,
    moderation_message: str = None
):
    """
    带日志记录的AI对话函数示例
    
    这个函数展示了如何在原有的 chat_with_ai 函数中集成日志记录功能
    """
    
    # 构建用户标识
    user_identifier = f"user_{current_user.id}"
    
    # 使用日志上下文管理器
    async with ChatLogContext(
        user=current_user,
        chat_request=chat_request,
        user_identifier=user_identifier,
        request=request,
        order_id=order_id,
        needs_new_charge=needs_new_charge,
        moderation_passed=moderation_passed,
        moderation_message=moderation_message
    ) as chat_log:
        
        try:
            # 更新状态为处理中
            await ai_chat_log_service.update_chat_log_processing(chat_log)
            
            if chat_request.response_mode == "streaming":
                # 流式模式处理
                return await _handle_streaming_response(
                    chat_request, dify_service, user_identifier, 
                    chat_log, current_user, order_id, needs_new_charge
                )
            else:
                # 阻塞模式处理
                return await _handle_blocking_response(
                    chat_request, dify_service, user_identifier,
                    chat_log, current_user, order_id, needs_new_charge
                )
                
        except Exception as e:
            # 异常会被上下文管理器自动处理
            logger.error(f"AI对话处理异常: {str(e)}")
            raise


async def _handle_streaming_response(
    chat_request: ChatMessageRequest,
    dify_service: DifyService,
    user_identifier: str,
    chat_log,
    current_user: User,
    order_id: str,
    needs_new_charge: bool
):
    """处理流式响应"""
    
    try:
        stream_generator = await dify_service.send_chat_message(
            query=chat_request.query,
            user=user_identifier,
            inputs=chat_request.inputs,
            response_mode=chat_request.response_mode,
            conversation_id=chat_request.conversation_id,
            files=chat_request.files,
            auto_generate_name=chat_request.auto_generate_name
        )
        
        # 包装流式生成器以记录日志
        async def logged_stream_wrapper():
            is_confirmed = False
            first_chunk_received = False
            answer_parts = []
            business_id = None
            
            try:
                async for chunk in stream_generator:
                    if not first_chunk_received:
                        first_chunk_received = True
                        logger.info(f"用户 {current_user.username} 收到第一个chunk")
                    
                    # 提取业务ID用于计费确认
                    if not is_confirmed and needs_new_charge:
                        business_id = wallet_service.extract_message_id_from_chunk(chunk)
                        if business_id:
                            # 更新日志中的业务ID
                            await ai_chat_log_service.update_charge_status(
                                chat_log, ChargeStatus.CONFIRMED, business_id
                            )
                            asyncio.create_task(
                                wallet_service.charge_confirm(current_user.id, order_id, business_id)
                            )
                            is_confirmed = True
                    
                    # 收集回答内容用于日志记录
                    try:
                        chunk_data = json.loads(chunk.replace("data: ", "").strip())
                        if chunk_data.get("event") == "message" and "answer" in chunk_data:
                            answer_parts.append(chunk_data["answer"])
                    except:
                        pass  # 忽略解析错误
                    
                    yield chunk
                
                # 流式响应完成，更新日志
                full_answer = "".join(answer_parts)
                await ai_chat_log_service.update_chat_log_success(
                    chat_log,
                    answer=full_answer,
                    business_id=business_id,
                    conversation_id=chat_request.conversation_id
                )
                
            except Exception as inner_error:
                # 流式响应错误处理
                if needs_new_charge and order_id:
                    await ai_chat_log_service.update_charge_status(
                        chat_log, ChargeStatus.CANCELLED
                    )
                    asyncio.create_task(
                        wallet_service.charge_cancel(current_user.id, order_id, business_id)
                    )
                
                error_msg = f"流式响应错误: {str(inner_error)}"
                await ai_chat_log_service.update_chat_log_error(chat_log, error_msg)
                
                yield f"data: {json.dumps({'status': 'error', 'content': error_msg})}\n\n"
        
        return StreamingResponse(
            logged_stream_wrapper(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
        
    except Exception as e:
        # 流式生成器创建失败
        if needs_new_charge and order_id:
            await ai_chat_log_service.update_charge_status(
                chat_log, ChargeStatus.CANCELLED
            )
            asyncio.create_task(
                wallet_service.charge_cancel(current_user.id, order_id, None)
            )
        
        error_msg = f"创建流式响应失败: {str(e)}"
        await ai_chat_log_service.update_chat_log_error(chat_log, error_msg)
        raise


async def _handle_blocking_response(
    chat_request: ChatMessageRequest,
    dify_service: DifyService,
    user_identifier: str,
    chat_log,
    current_user: User,
    order_id: str,
    needs_new_charge: bool
):
    """处理阻塞响应"""
    
    try:
        result = await dify_service.send_chat_message(
            query=chat_request.query,
            user=user_identifier,
            inputs=chat_request.inputs,
            response_mode=chat_request.response_mode,
            conversation_id=chat_request.conversation_id,
            files=chat_request.files,
            auto_generate_name=chat_request.auto_generate_name
        )
        
        # 提取响应信息
        message_id = result.get('message_id')
        conversation_id = result.get('conversation_id')
        answer = result.get('answer', '')
        
        # 更新日志为成功状态
        await ai_chat_log_service.update_chat_log_success(
            chat_log,
            answer=answer,
            message_id=message_id,
            conversation_id=conversation_id,
            business_id=conversation_id
        )
        
        # 计费确认
        if needs_new_charge and order_id:
            await ai_chat_log_service.update_charge_status(
                chat_log, ChargeStatus.CONFIRMED, conversation_id
            )
            asyncio.create_task(
                wallet_service.charge_confirm(current_user.id, order_id, conversation_id)
            )
        
        logger.info(f"AI对话完成，用户: {current_user.username}, 消息ID: {message_id}")
        return result
        
    except Exception as e:
        # 阻塞响应错误处理
        if needs_new_charge and order_id:
            await ai_chat_log_service.update_charge_status(
                chat_log, ChargeStatus.CANCELLED
            )
            asyncio.create_task(
                wallet_service.charge_cancel(current_user.id, order_id, None)
            )
        
        error_msg = f"Dify服务错误: {str(e)}"
        await ai_chat_log_service.update_chat_log_error(chat_log, error_msg)
        raise


# 使用示例：
"""
在原有的 chat_with_ai 函数中，可以这样集成日志记录：

@router.post("/chat", summary="AI对话接口（流式输出）")
async def chat_with_ai(
    request: Request,
    chat_request: ChatMessageRequest
):
    # ... 原有的用户认证、内容审核、计费逻辑 ...
    
    # 使用带日志记录的函数
    return await chat_with_ai_with_logging(
        request=request,
        chat_request=chat_request,
        current_user=current_user,
        dify_service=dify_service,
        order_id=order_id,
        needs_new_charge=needs_new_charge,
        moderation_passed=moderation_passed,
        moderation_message=moderation_message
    )
"""
