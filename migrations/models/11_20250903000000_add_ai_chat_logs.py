from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "ai_chat_logs" (
            "id" UUID NOT NULL PRIMARY KEY,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "user_id" UUID NOT NULL,
            "user_identifier" VARCHAR(100) NOT NULL,
            "query" TEXT NOT NULL,
            "inputs" JSONB,
            "response_mode" VARCHAR(20) NOT NULL DEFAULT 'streaming',
            "auto_generate_name" BOOL NOT NULL DEFAULT True,
            "files_info" JSONB,
            "conversation_id" VARCHAR(100),
            "message_id" VARCHAR(100),
            "parent_message_id" VARCHAR(100),
            "answer" TEXT,
            "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
            "error_message" TEXT,
            "response_time_ms" INT,
            "order_id" VARCHAR(100),
            "business_id" VARCHAR(100),
            "needs_new_charge" BOOL NOT NULL DEFAULT True,
            "charge_status" VARCHAR(20) NOT NULL DEFAULT 'pending',
            "moderation_passed" BOOL NOT NULL DEFAULT True,
            "moderation_message" VARCHAR(500),
            "request_ip" VARCHAR(45),
            "user_agent" VARCHAR(500),
            "session_info" JSONB,
            "extra_data" JSONB,
            CONSTRAINT "fk_ai_chat_logs_user_id" FOREIGN KEY ("user_id") REFERENCES "users" ("id") ON DELETE CASCADE
        );
        
        COMMENT ON TABLE "ai_chat_logs" IS 'AI对话日志表';
        COMMENT ON COLUMN "ai_chat_logs"."id" IS '主键';
        COMMENT ON COLUMN "ai_chat_logs"."created_at" IS '创建时间';
        COMMENT ON COLUMN "ai_chat_logs"."updated_at" IS '更新时间';
        COMMENT ON COLUMN "ai_chat_logs"."user_id" IS '用户ID';
        COMMENT ON COLUMN "ai_chat_logs"."user_identifier" IS 'Dify用户标识';
        COMMENT ON COLUMN "ai_chat_logs"."query" IS '用户输入的问题';
        COMMENT ON COLUMN "ai_chat_logs"."inputs" IS '应用变量值';
        COMMENT ON COLUMN "ai_chat_logs"."response_mode" IS '响应模式(streaming/blocking)';
        COMMENT ON COLUMN "ai_chat_logs"."auto_generate_name" IS '是否自动生成会话标题';
        COMMENT ON COLUMN "ai_chat_logs"."files_info" IS '上传文件信息';
        COMMENT ON COLUMN "ai_chat_logs"."conversation_id" IS 'Dify会话ID';
        COMMENT ON COLUMN "ai_chat_logs"."message_id" IS 'Dify消息ID';
        COMMENT ON COLUMN "ai_chat_logs"."parent_message_id" IS '父消息ID';
        COMMENT ON COLUMN "ai_chat_logs"."answer" IS 'AI回复内容';
        COMMENT ON COLUMN "ai_chat_logs"."status" IS '处理状态';
        COMMENT ON COLUMN "ai_chat_logs"."error_message" IS '错误信息';
        COMMENT ON COLUMN "ai_chat_logs"."response_time_ms" IS '响应时间(毫秒)';
        COMMENT ON COLUMN "ai_chat_logs"."order_id" IS '预扣费订单ID';
        COMMENT ON COLUMN "ai_chat_logs"."business_id" IS '业务ID';
        COMMENT ON COLUMN "ai_chat_logs"."needs_new_charge" IS '是否需要重新计费';
        COMMENT ON COLUMN "ai_chat_logs"."charge_status" IS '计费状态';
        COMMENT ON COLUMN "ai_chat_logs"."moderation_passed" IS '内容审核是否通过';
        COMMENT ON COLUMN "ai_chat_logs"."moderation_message" IS '审核失败原因';
        COMMENT ON COLUMN "ai_chat_logs"."request_ip" IS '请求IP地址';
        COMMENT ON COLUMN "ai_chat_logs"."user_agent" IS '用户代理';
        COMMENT ON COLUMN "ai_chat_logs"."session_info" IS '会话相关信息';
        COMMENT ON COLUMN "ai_chat_logs"."extra_data" IS '扩展数据';
        
        -- 创建索引
        CREATE INDEX "idx_ai_chat_logs_user_created" ON "ai_chat_logs" ("user_id", "created_at");
        CREATE INDEX "idx_ai_chat_logs_conversation_created" ON "ai_chat_logs" ("conversation_id", "created_at");
        CREATE INDEX "idx_ai_chat_logs_status_created" ON "ai_chat_logs" ("status", "created_at");
        CREATE INDEX "idx_ai_chat_logs_order_id" ON "ai_chat_logs" ("order_id");
        CREATE INDEX "idx_ai_chat_logs_message_id" ON "ai_chat_logs" ("message_id");
        CREATE INDEX "idx_ai_chat_logs_business_id" ON "ai_chat_logs" ("business_id");
        
        -- 创建部分索引（仅对非空值建索引，提高性能）
        CREATE INDEX "idx_ai_chat_logs_conversation_id_not_null" ON "ai_chat_logs" ("conversation_id") WHERE "conversation_id" IS NOT NULL;
        CREATE INDEX "idx_ai_chat_logs_message_id_not_null" ON "ai_chat_logs" ("message_id") WHERE "message_id" IS NOT NULL;
        CREATE INDEX "idx_ai_chat_logs_order_id_not_null" ON "ai_chat_logs" ("order_id") WHERE "order_id" IS NOT NULL;
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "ai_chat_logs";
    """
